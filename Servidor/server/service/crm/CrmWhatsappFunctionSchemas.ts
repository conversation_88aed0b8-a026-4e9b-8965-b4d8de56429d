/**
 * Definições JSON Schema das funções para OpenAI Function Calling
 * Permite que o ChatGPT chame automaticamente as tools do assistente de vendas
 */

export const CRM_WHATSAPP_FUNCTIONS = [
  {
    name: "buscar_info_concorrente",
    description: "Busca informações detalhadas sobre sistemas concorrentes como iFood, Uber Eats, etc. Use quando o lead mencionar um concorrente ou para fazer comparações.",
    parameters: {
      type: "object",
      properties: {
        nome_concorrente: {
          type: "string",
          description: "Nome do sistema concorrente (ex: ifood, uber eats, rappi, aiqfome, goomer)",
          enum: ["ifood", "uber eats", "rappi", "aiqfome", "goomer"]
        },
        funcionalidades_especificas: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Lista de funcionalidades específicas para comparar (ex: taxas, suporte, limitacoes)"
        }
      },
      required: ["nome_concorrente"]
    }
  },
  {
    name: "obter_beneficios_cardapio",
    description: "Obtém benefícios específicos do Meu Cardápio por categoria. Use sempre que precisar mencionar funcionalidades ou vantagens específicas do produto.",
    parameters: {
      type: "object",
      properties: {
        categoria: {
          type: "string",
          description: "Categoria de benefícios a buscar",
          enum: ["delivery", "fidelidade", "automacao", "gestao", "marketing", "geral"]
        },
        segmento_cliente: {
          type: "string",
          description: "Segmento do cliente para personalizar benefícios (ex: pizzaria, hamburgueria, açaiteria)"
        }
      },
      required: ["categoria"]
    }
  },
  {
    name: "consultar_dados_lead",
    description: "Consulta dados enriquecidos do lead para personalizar a abordagem. Use para obter insights sobre o cliente.",
    parameters: {
      type: "object",
      properties: {
        telefone: {
          type: "string",
          description: "Telefone do lead para buscar dados"
        }
      },
      required: [] as string[]
    }
  },
  {
    name: "analisar_perfil_instagram",
    description: "Analisa o perfil do Instagram do lead para identificar oportunidades. Use quando o lead tem Instagram ativo.",
    parameters: {
      type: "object",
      properties: {
        instagram_handle: {
          type: "string",
          description: "Handle do Instagram (sem @)"
        },
        bio: {
          type: "string",
          description: "Bio do Instagram"
        },
        seguidores: {
          type: "number",
          description: "Número de seguidores"
        }
      },
      required: ["instagram_handle"]
    }
  },
  {
    name: "obter_info_integracoes",
    description: "Obtém informações sobre integrações disponíveis com PDVs, sistemas de pagamento, etc. Use quando lead perguntar sobre integrações.",
    parameters: {
      type: "object",
      properties: {
        sistema_pdv: {
          type: "string",
          description: "Sistema de PDV atual do cliente (ex: stone, cielo lio, pagseguro)"
        },
        delivery_apps: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Apps de delivery que o cliente usa"
        },
        sistema_pagamento: {
          type: "string",
          description: "Sistema de pagamento atual (ex: mercado pago, pagseguro)"
        }
      },
      required: [] as string[]
    }
  },
  {
    name: "buscar_caso_sucesso",
    description: "Busca case de sucesso similar ao lead para usar como prova social. Use para dar exemplos concretos e credibilidade.",
    parameters: {
      type: "object",
      properties: {
        segmento: {
          type: "string",
          description: "Segmento do negócio (ex: pizzaria, hamburgueria, açaiteria)"
        },
        tamanho_empresa: {
          type: "string",
          description: "Tamanho da empresa",
          enum: ["pequeno", "médio", "grande"]
        },
        regiao: {
          type: "string",
          description: "Região/cidade do cliente"
        },
        problema_principal: {
          type: "string",
          description: "Principal problema que o cliente tem"
        }
      },
      required: ["segmento"]
    }
  },
  {
    name: "analisar_objecao",
    description: "Analisa uma objeção específica e fornece estratégias para responder. Use quando o lead fizer objeções.",
    parameters: {
      type: "object",
      properties: {
        tipo_objecao: {
          type: "string",
          description: "Tipo de objeção do cliente",
          enum: ["preco", "mudanca", "tempo", "confianca", "funcionalidade", "decisao"]
        },
        contexto_conversa: {
          type: "string",
          description: "Contexto da conversa onde a objeção foi feita"
        },
        fase_spin: {
          type: "string",
          description: "Fase SPIN atual da conversa",
          enum: ["situacao", "problema", "implicacao", "necessidade"]
        }
      },
      required: ["tipo_objecao"]
    }
  },
  {
    name: "gerar_rapport_personalizado",
    description: "Gera estratégias de rapport personalizadas baseadas no perfil do lead. Use para primeira abordagem ou reativação.",
    parameters: {
      type: "object",
      properties: {
        nome_lead: {
          type: "string",
          description: "Nome do lead"
        },
        empresa: {
          type: "string",
          description: "Nome da empresa"
        },
        instagram_handle: {
          type: "string",
          description: "Handle do Instagram"
        },
        bio_instagram: {
          type: "string",
          description: "Bio do Instagram"
        },
        seguidores: {
          type: "number",
          description: "Número de seguidores"
        },
        cidade: {
          type: "string",
          description: "Cidade do lead"
        },
        tentativas_anteriores: {
          type: "number",
          description: "Número de tentativas de contato anteriores"
        },
        ultimo_contato: {
          type: "string",
          description: "Data do último contato"
        }
      },
      required: [] as string[]
    }
  }
];

/**
 * Configuração para function calling
 */
export const FUNCTION_CALL_CONFIG = {
  // Permite o ChatGPT escolher automaticamente quando chamar funções
  function_call: "auto"
};

/**
 * Helper para obter função específica por nome
 */
export function getFunctionByName(name: string) {
  return CRM_WHATSAPP_FUNCTIONS.find(func => func.name === name);
}

/**
 * Helper para validar se uma função existe
 */
export function isFunctionValid(name: string): boolean {
  return CRM_WHATSAPP_FUNCTIONS.some(func => func.name === name);
}